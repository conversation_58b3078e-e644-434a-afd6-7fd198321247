import Foundation

// 1. 全局图鉴模板（静态数据）
struct PetTemplate: Codable, Identifiable {
    let id = UUID()
    let name: String
    let imageName: String
    let rarity: Int
    
    private enum CodingKeys: String, CodingKey {
        case name, imageName, rarity
    }
}

// 2. 用户宠物（动态数据）
struct UserPet: Codable, Identifiable {
    let id: UUID
    let templateName: String  // 对应 PetTemplate.name
    var level: Int
    var experience: Int
    var lastFeedTime: Date?
}
