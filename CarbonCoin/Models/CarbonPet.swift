//
//  CarbonPet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation
import CloudKit

/// 碳宠物模型
struct CarbonPet: Codable, Identifiable {
    let id: UUID
    let userId: UUID? // 从JSON加载时可能为空
    var name: String
    var level: Int
    var experience: Int
    var rarity: Int // 稀有度，用于显示星星
    var imageName: String // 宠物图片名称
    var feedCost: Int
    var lastFeedTime: Date?
    var createdAt: Date?
    var updatedAt: Date?
    
    init(id: UUID = UUID(),
         userId: UUID?,
         name: String,
         level: Int = 1,
         experience: Int = 0,
         rarity: Int = 1,
         imageName: String) {
        self.id = id
        self.userId = userId
        self.name = name
        self.level = level
        self.experience = experience
        self.rarity = rarity
        self.imageName = imageName
        self.feedCost = level * 5 // 喂养成本随等级增加
        self.lastFeedTime = nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    /// 经验值升级阈值
    var experienceThreshold: Int {
        return level * 100
    }
    
    /// 经验值进度（0.0 - 1.0）
    var experienceProgress: Double {
        return Double(experience) / Double(experienceThreshold)
    }
    
    /// 是否可以升级
    var canLevelUp: Bool {
        return experience >= experienceThreshold
    }
    
    /// 升级（MVP阶段暂不实现，但保留接口）
    mutating func levelUp() {
        if canLevelUp {
            level += 1
            experience -= experienceThreshold
            feedCost = level * 5
            updatedAt = Date()
        }
    }
    
    /// 喂养
    mutating func feed(experience: Int = 10) {
        self.experience += experience
        self.lastFeedTime = Date()
        self.updatedAt = Date()
        
        // 自动升级（MVP阶段可选）
        // if canLevelUp {
        //     levelUp()
        // }
    }
    
}

// MARK: - CloudKit Support
extension CarbonPet {
    /// CloudKit记录类型名称
    static let recordType = "CarbonPet"
    
    /// 从CloudKit记录创建碳宠物
    init?(from record: CKRecord) {
        guard let userIdString = record["userId"] as? String,
              let userId = UUID(uuidString: userIdString),
              let name = record["name"] as? String,
              let level = record["level"] as? Int,
              let experience = record["experience"] as? Int,
              let feedCost = record["feedCost"] as? Int,
              let rarity = record["rarity"] as? Int,
              let imageName = record["imageName"] as? String else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.userId = userId
        self.name = name
        self.level = level
        self.experience = experience
        self.rarity = rarity
        self.imageName = imageName
        self.feedCost = feedCost
        self.lastFeedTime = record["lastFeedTime"] as? Date
        self.createdAt = record["createdAt"] as? Date
        self.updatedAt = record["updatedAt"] as? Date
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: CarbonPet.recordType, recordID: recordID)
        
        record["userId"] = userId?.uuidString
        record["name"] = name
        record["level"] = level
        record["experience"] = experience
        record["rarity"] = rarity
        record["imageName"] = imageName
        record["feedCost"] = feedCost
        record["lastFeedTime"] = lastFeedTime
        record["createdAt"] = createdAt
        record["updatedAt"] = updatedAt
        
        return record
    }
}
