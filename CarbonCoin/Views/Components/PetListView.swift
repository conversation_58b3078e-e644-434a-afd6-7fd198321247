import SwiftUI

struct PetListView: View {
    @ObservedObject var viewModel: CarbonPetViewModel
    
    private let columns = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 20) {
                ForEach(viewModel.allPets) { pet in
                    PetItemView(
                        pet: pet,
                        isOwned: viewModel.isPetOwned(petId: pet.id)
                    )
                }
            }
            .padding()
        }
    }
}

#Preview {
    let viewModel = CarbonPetViewModel()
    return PetListView(viewModel: viewModel)
        .background(Color.black.ignoresSafeArea())
}
