import SwiftUI

struct PetItemView: View {
    let pet: CarbonPet
    let isOwned: Bool
    
    private let cardWidth: CGFloat = 180
    private let cardHeight: CGFloat = 220
    
    var body: some View {
        ZStack(alignment: .bottom) {
            // 背景
            RoundedRectangle(cornerRadius: 20)
                .fill(LinearGradient.petItemBgColor)
                .frame(width: cardWidth, height: cardHeight)
                .shadow(color: .black.opacity(0.3), radius: 10, y: 5)

            // 宠物 PDF（允许超出）
            if let petImage = UIImage(named: pet.imageName) {
                Image(uiImage: petImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: cardWidth * 1.2, height: cardHeight * 1.2)
                    .offset(y: -50) // 向上偏移，使其突出
            }
            
            // 等级标签（右上角，倾斜）
            levelTag
                .offset(x: cardWidth / 2 - 30, y: -cardHeight / 2 + 20)

            // 底部信息栏
            bottomInfoBar
        }
        .frame(width: cardWidth, height: cardHeight)
//        .clipped() // 裁剪掉超出 ZStack 范围的部分
    }
    
    // 等级标签
    private var levelTag: some View {
        Text("Lv\(pet.level)")
            .font(.system(size: 14, weight: .bold))
            .foregroundColor(.white)
            .padding(.horizontal, 10)
            .padding(.vertical, 5)
            .background(
                ZStack {
                    VisualEffectView(effect: UIBlurEffect(style: .dark))
                    Color.black.opacity(0.2)
                }
                .clipShape(Capsule())
            )
            .rotationEffect(.degrees(-20))
    }
    
    // 底部信息栏
    private var bottomInfoBar: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(pet.name)
                    .font(.headline)
                    .foregroundColor(.white)
                
                HStack(spacing: 2) {
                    ForEach(0..<pet.rarity, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.system(size: 12))
                    }
                }
            }
            
            Spacer()
            
            if isOwned {
                Text("已获得")
                    .font(.caption)
                    .bold()
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(Color.green.opacity(0.8))
                    .clipShape(Capsule())
            }
        }
        .padding(12)
        .background(
            Color.black.opacity(0.4)
                .blur(radius: 10)
        )
        .clipShape(RoundedCorner(radius: 20, corners: [.bottomLeft, .bottomRight]))
    }
}

// MARK: - Preview
#Preview {
    let mockPetOwned = CarbonPet(
        userId: nil,
        name: "小碳",
        level: 5,
        rarity: 2,
        imageName: "pet_小碳"
    )
    
    let mockPetNotOwned = CarbonPet(
        userId: nil,
        name: "贰负",
        level: 10,
        rarity: 3,
        imageName: "pet_贰负"
    )
    
    return ZStack {
        Color.black.ignoresSafeArea()
        VStack(spacing: 20) {
            PetItemView(pet: mockPetOwned, isOwned: true)
            PetItemView(pet: mockPetNotOwned, isOwned: false)
        }
    }
}

// Helper for specific corner radius
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}
