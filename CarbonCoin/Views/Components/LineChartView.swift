//
//  LineChartView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import SwiftUI
import Charts

// MARK: - 通用折线图组件
struct LineChartView: View {
    
    // MARK: - Properties
    let stepData: [StepData]
    let averageValue: Double
    let yAxisMin: Int
    let yAxisMax: Int
    let selectedPeriod: TimePeriod
    
    // MARK: - State
    @State private var animationProgress: Double = 0
    @State private var showAverage: Bool = true
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            if stepData.isEmpty {
                emptyStateView
            } else {
                chartView
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                animationProgress = 1.0
            }
        }
        .onChange(of: stepData) { _ in
            // 数据变化时重新播放动画
            animationProgress = 0
            withAnimation(.easeInOut(duration: 0.8)) {
                animationProgress = 1.0
            }
        }
    }
    
    // MARK: - Chart View
    private var chartView: some View {
        Chart {
            // 折线图数据
            ForEach(Array(stepData.enumerated()), id: \.element.id) { index, data in
                LineMark(
                    x: .value("时间", getXAxisValue(for: data, index: index)),
                    y: .value("步数", data.steps)
                )
                .foregroundStyle(Color.brandColor)
                .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                .interpolationMethod(.catmullRom)
                
                // 数据点
                PointMark(
                    x: .value("时间", getXAxisValue(for: data, index: index)),
                    y: .value("步数", data.steps)
                )
                .foregroundStyle(Color.brandColor)
                .symbolSize(40)
                .opacity(animationProgress)
                
                // 渐变填充区域
                AreaMark(
                    x: .value("时间", getXAxisValue(for: data, index: index)),
                    yStart: .value("起始", yAxisMin),
                    yEnd: .value("步数", data.steps)
                )
                .foregroundStyle(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.brandColor.opacity(0.3),
                            Color.brandColor.opacity(0.1),
                            Color.clear
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .interpolationMethod(.catmullRom)
                .opacity(animationProgress * 0.6)
            }
            
            // 平均值线
            if showAverage && averageValue > 0 {
                RuleMark(y: .value("平均值", averageValue))
                    .foregroundStyle(Color.auxiliaryYellow)
                    .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
                    .opacity(animationProgress * 0.8)
                    .annotation(position: .topTrailing, alignment: .leading) {
                        Text("平均: \(Int(averageValue))")
                            .font(.captionBrand)
                            .foregroundColor(.auxiliaryYellow)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color.auxiliaryYellow.opacity(0.2))
                            )
                            .opacity(animationProgress)
                    }
            }
        }
        .chartYScale(domain: yAxisMin...yAxisMax)
        .chartXAxis {
            AxisMarks(values: getXAxisMarks()) { value in
                AxisGridLine()
                    .foregroundStyle(Color.white.opacity(0.1))
                AxisTick()
                    .foregroundStyle(Color.white.opacity(0.3))
                AxisValueLabel {
                    if let stringValue = value.as(String.self) {
                        Text(stringValue)
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                            .fixedSize() // 避免截断
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading, values: getYAxisMarks()) { value in
                AxisGridLine()
                    .foregroundStyle(Color.white.opacity(0.1))
                AxisTick()
                    .foregroundStyle(Color.white.opacity(0.3))
                AxisValueLabel {
                    if let intValue = value.as(Int.self) {
                        Text(formatYAxisValue(intValue))
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                            .padding(.horizontal, 0)
                    }
                }
            }
        }
        .frame(height: 290)
        .padding(.horizontal, Theme.Spacing.xs)
        .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.md))
    }
    
    // MARK: - Empty State View
    private var emptyStateView: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.largeTitle)
                .foregroundColor(.textSecondary)
            
            Text("暂无数据")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
        .frame(height: 200)
    }
    
    // MARK: - Helper Methods
    
    /// 获取X轴显示值
    private func getXAxisValue(for data: StepData, index: Int) -> String {
        let formatter = DateFormatter()
        
        switch selectedPeriod {
        case .day:
            formatter.dateFormat = "HH"
            return formatter.string(from: data.date)
        case .week:
            formatter.dateFormat = "E"
            return formatter.string(from: data.date)
        case .month:
            formatter.dateFormat = "d"
            return formatter.string(from: data.date)
        case .sixMonths:
            formatter.dateFormat = "M/d"
            return formatter.string(from: data.date)
        }
    }
    
    /// 获取X轴刻度标记
    private func getXAxisMarks() -> [String] {
        let step = max(1, stepData.count / 6) // 最多显示6个刻度
        return stride(from: 0, to: stepData.count, by: step).compactMap { index in
            guard index < stepData.count else { return nil }
            return getXAxisValue(for: stepData[index], index: index)
        }
    }
    
    /// 获取Y轴刻度标记
    private func getYAxisMarks() -> [Int] {
        let range = yAxisMax - yAxisMin
        let step = max(1000, range / 5) // 大约5个刻度
        return stride(from: yAxisMin, through: yAxisMax, by: step).map { $0 }
    }
    
    /// 格式化Y轴数值
    private func formatYAxisValue(_ value: Int) -> String {
        if value >= 10000 {
            return "\(value / 1000)k"
        } else {
            return "\(value)"
        }
    }
}

// MARK: - Preview
#Preview {
    let mockData = [
        StepData(date: Date().addingTimeInterval(-6*24*3600), steps: 8500),
        StepData(date: Date().addingTimeInterval(-5*24*3600), steps: 12000),
        StepData(date: Date().addingTimeInterval(-4*24*3600), steps: 9800),
        StepData(date: Date().addingTimeInterval(-3*24*3600), steps: 11500),
        StepData(date: Date().addingTimeInterval(-2*24*3600), steps: 7200),
        StepData(date: Date().addingTimeInterval(-1*24*3600), steps: 10800),
        StepData(date: Date(), steps: 9500)
    ]
    
    return VStack {
        LineChartView(
            stepData: mockData,
            averageValue: 9900,
            yAxisMin: 5000,
            yAxisMax: 15000,
            selectedPeriod: .week
        )
        .padding()
        .glassCard()
    }
    .padding()
    .stableBackground()
}
