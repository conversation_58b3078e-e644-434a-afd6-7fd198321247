//
//  StepsStatisticsCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import SwiftUI

// MARK: - 步数统计卡片组件
struct StepsStatisticsCard: View {
    
    // MARK: - Properties
    @StateObject private var viewModel = StepsViewModel()
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            headerView
            chartContainerView
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
        .refreshable {
            await viewModel.refreshData()
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text("步数统计")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)
                
                if let statistics = viewModel.statistics {
                    HStack(spacing: Theme.Spacing.xs) {
                        Text("\(viewModel.getChangePercentageText())")
                            .font(.captionBrand)
                            .foregroundColor(viewModel.getChangePercentageColor())
                    }
                }
            }
            
            Spacer()
            
            // 时间段选择器
            CompactTimePeriodSelector(selectedPeriod: $viewModel.selectedPeriod) { period in
                viewModel.selectPeriod(period)
            }
        }
    }
    
    // MARK: - Chart Container View
    private var chartContainerView: some View {
        VStack(spacing: Theme.Spacing.sm) {
            if viewModel.isLoading {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(errorMessage)
            } else {
                chartView
                statisticsView
            }
        }
    }
    
    // MARK: - Chart View
    private var chartView: some View {
        LineChartView(
            stepData: viewModel.currentStepData,
            averageValue: viewModel.getAverageSteps(),
            yAxisMin: viewModel.yAxisMinValue,
            yAxisMax: viewModel.yAxisMaxValue,
            selectedPeriod: viewModel.selectedPeriod
        )
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground.opacity(0.3))
        )
    }
    
    // MARK: - Statistics View
    private var statisticsView: some View {
        HStack(spacing: Theme.Spacing.lg) {
            if let statistics = viewModel.statistics {
                statisticItem(
                    title: "总步数",
                    value: formatNumber(statistics.totalSteps),
                    icon: "figure.walk"
                )
                
                Divider()
                    .frame(height: 30)
                    .background(Color.textSecondary.opacity(0.3))
                
                statisticItem(
                    title: "平均",
                    value: formatNumber(Int(statistics.averageSteps)),
                    icon: "chart.line.uptrend.xyaxis"
                )
                
                Divider()
                    .frame(height: 30)
                    .background(Color.textSecondary.opacity(0.3))
                
                statisticItem(
                    title: "最高",
                    value: formatNumber(statistics.maxSteps),
                    icon: "arrow.up.circle"
                )
            }
        }
        .padding(.horizontal, Theme.Spacing.sm)
    }
    
    // MARK: - Statistic Item
    private func statisticItem(title: String, value: String, icon: String) -> some View {
        VStack(spacing: Theme.Spacing.xs) {
            HStack(spacing: Theme.Spacing.xs) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(.brandColor)
                
                Text(title)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
            
            Text(value)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: Theme.Spacing.md) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .brandColor))
                .scaleEffect(1.2)
            
            Text("加载中...")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
        .frame(height: 200)
    }
    
    // MARK: - Error View
    private func errorView(_ message: String) -> some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.error)
            
            Text(message)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
            
            Button("重试") {
                Task {
                    await viewModel.refreshData()
                }
            }
            .buttonStyle(SecondaryButtonStyle())
        }
        .frame(height: 200)
    }
    
    // MARK: - Helper Methods
    
    /// 格式化数字显示
    private func formatNumber(_ number: Int) -> String {
        if number >= 10000 {
            return String(format: "%.1fk", Double(number) / 1000.0)
        } else {
            return "\(number)"
        }
    }
}

// MARK: - Compact Steps Statistics Card
/// 紧凑版步数统计卡片（用于较小的空间）
struct CompactStepsStatisticsCard: View {
    
    @StateObject private var viewModel = StepsViewModel()
    
    var body: some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack {
                Text("步数")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                
                Spacer()
                
                CompactTimePeriodSelector(selectedPeriod: $viewModel.selectedPeriod) { period in
                    viewModel.selectPeriod(period)
                }
            }
            
            if let statistics = viewModel.statistics {
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("\(formatNumber(Int(statistics.averageSteps)))")
                            .font(.title2Brand)
                            .foregroundColor(.textPrimary)
                        
                        Text("平均步数")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                    }
                    
                    Spacer()
                    
                    Text(viewModel.getChangePercentageText())
                        .font(.captionBrand)
                        .foregroundColor(viewModel.getChangePercentageColor())
                }
            }
        }
        .padding(Theme.Spacing.md)
        .glassCard()
    }
    
    private func formatNumber(_ number: Int) -> String {
        if number >= 10000 {
            return String(format: "%.1fk", Double(number) / 1000.0)
        } else {
            return "\(number)"
        }
    }
}

// MARK: - Preview
#Preview("完整卡片") {
    StepsStatisticsCard()
        .padding()
        .stableBackground()
}

#Preview("紧凑卡片") {
    VStack(spacing: 20) {
        StepsStatisticsCard()
        CompactStepsStatisticsCard()
    }
    .padding()
    .stableBackground()
}
