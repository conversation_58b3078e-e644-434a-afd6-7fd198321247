//
//  FootprintView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct FootprintView: View {
    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                ScrollView {
                    VStack(spacing: Theme.Spacing.lg) {
                        UserGreetingCard()
                        AnnouncementCard()
                        MapEntryButton()
                        StepsStatisticsCard()
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
                .scrollContentBackground(.hidden) // 隐藏 ScrollView 默认背景
            }
            .navigationTitle("足迹")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }
}

// MARK: - User Greeting Card
struct UserGreetingCard: View {
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                Text("Hi, 用户")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)

                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.auxiliaryYellow)

                    Text("碳币: 128")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    Spacer()

                    HStack(spacing: 4) {
                        Text("↗ 12%")
                            .font(.captionBrand)
                            .foregroundColor(.success)

                        Image(systemName: "arrow.up.right")
                            .font(.caption)
                            .foregroundColor(.success)
                    }
                }
            }

            Spacer()
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Announcement Card
struct AnnouncementCard: View {
    var body: some View {
        HStack {
            Image(systemName: "info.circle.fill")
                .foregroundColor(.skyBlue)
                .font(.title3)

            VStack(alignment: .leading, spacing: 4) {
                Text("系统公告")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Text("敏感地图已经维修完毕！")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
        .padding(Theme.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .stroke(Color.skyBlue.opacity(0.3), lineWidth: 2)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .fill(Color.skyBlue.opacity(0.1))
                )
        )
    }
}

// MARK: - Map Entry Button
struct MapEntryButton: View {
    var body: some View {
        Button(action: {
            // TODO: 打开地图页面
        }) {
            HStack {
                Image(systemName: "map.fill")
                    .font(.title2)
                    .foregroundColor(.textPrimary)

                VStack(alignment: .leading) {
                    Text("探索地图")
                        .font(.title3Brand)
                        .foregroundColor(.textPrimary)

                    Text("查看轨迹和好友位置")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.textSecondary)
            }
            .padding(Theme.Spacing.lg)
        }
        .buttonStyle(PrimaryButtonStyle())
    }
}



#Preview {
    FootprintView()
        .stableBackground()
}
