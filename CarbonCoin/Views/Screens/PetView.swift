//
//  PetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct PetView: View {
    @StateObject private var viewModel = CarbonPetViewModel()

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                Color.black.ignoresSafeArea()
                
                // 宠物列表
                PetListView(viewModel: viewModel)
            }
            .navigationTitle("宠物图鉴")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>(action: {
                        // TODO: Add filter or sort action
                    }) {
                        Image(systemName: "slider.horizontal.3")
                    }
                }
            }
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }
}

#Preview {
    PetView()
}
