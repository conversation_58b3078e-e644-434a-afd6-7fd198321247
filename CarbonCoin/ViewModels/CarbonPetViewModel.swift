import Foundation
import Combine
import UIKit

class CarbonPetViewModel: ObservableObject {
    // 发布的数据
    @Published var allPets: [CarbonPet] = []
    @Published var userPets: [CarbonPet] = []
    @Published var isLoading = false

    // 本地存储 Key
    private let userPetsKey = "user_pets"

    init() {
        loadDefaultPets()
    }

    /// 加载默认宠物数据（从 Bundle JSON）
    func loadDefaultPets() {
        guard let url = Bundle.main.url(forResource: "pets", withExtension: "json"),
              let data = try? Data(contentsOf: url) else {
            print("Failed to find or load pets.json")
            return
        }
        
        let decoder = JSONDecoder()
        do {
            let pets = try decoder.decode([CarbonPet].self, from: data)
            DispatchQueue.main.async {
                self.allPets = pets
                self.loadUserPets() // Move user pets loading here
            }
        } catch {
            print("Failed to decode pets.json: \(error)")
        }
    }

    /// 加载用户已获得宠物（从 UserDefaults 或本地 JSON）
    func loadUserPets() {
        if let data = UserDefaults.standard.data(forKey: userPetsKey),
           let pets = try? JSONDecoder().decode([CarbonPet].self, from: data) {
            userPets = pets
        } else {
            // For testing, let's assume the user owns the first pet by default
            if let firstPet = allPets.first {
                userPets = [firstPet]
                saveUserPets()
            }
        }
    }

    /// 保存用户宠物数据（到 UserDefaults）
    func saveUserPets() {
        if let data = try? JSONEncoder().encode(userPets) {
            UserDefaults.standard.set(data, forKey: userPetsKey)
        }
    }

    /// 喂养宠物
    func feedPet(petId: UUID, experience: Int = 10) {
        if let index = userPets.firstIndex(where: { $0.id == petId }) {
            userPets[index].feed(experience: experience)
            if userPets[index].canLevelUp {
                userPets[index].levelUp()
            }
            saveUserPets()
            objectWillChange.send() // Manually notify views of the change
        }
    }

    /// 解锁新宠物（从 allPets 添加到 userPets）
    func unlockPet(petId: UUID) {
        guard let pet = allPets.first(where: { $0.id == petId }),
              !userPets.contains(where: { $0.id == petId }) else {
            return
        }
        userPets.append(pet)
        saveUserPets()
    }

    /// 检查用户是否拥有特定宠物
    func isPetOwned(petId: UUID) -> Bool {
        return userPets.contains(where: { $0.id == petId })
    }

    /// 获取宠物图像（从 Assets）
    func getPetImage(name: String) -> UIImage? {
        return UIImage(named: name)
    }
}
