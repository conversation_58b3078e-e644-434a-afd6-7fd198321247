//
//  StepsViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import Foundation
import SwiftUI

// MARK: - 步数统计ViewModel
@MainActor
class StepsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    /// 当前选中的时间段
    @Published var selectedPeriod: TimePeriod = .week
    
    /// 当前显示的步数数据
    @Published var currentStepData: [StepData] = []
    
    /// 统计信息
    @Published var statistics: SportsStatistics?
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// Y轴最大值（用于图表刻度）
    @Published var yAxisMaxValue: Int = 15000
    
    /// Y轴最小值（用于图表刻度）
    @Published var yAxisMinValue: Int = 0
    
    // MARK: - Private Properties
    private var allStepData: [StepData] = []
    
    // MARK: - Initialization
    init() {
        generateMockData()
        updateDataForSelectedPeriod()
    }
    
    // MARK: - Public Methods
    
    /// 切换时间段
    func selectPeriod(_ period: TimePeriod) {
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedPeriod = period
            updateDataForSelectedPeriod()
        }
    }
    
    /// 刷新数据
    func refreshData() async {
        isLoading = true
        errorMessage = nil
        
        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        // 重新生成mock数据
        generateMockData()
        updateDataForSelectedPeriod()
        
        isLoading = false
    }
    
    /// 获取平均值（用于绘制平均线）
    func getAverageSteps() -> Double {
        return statistics?.averageSteps ?? 0
    }
    
    /// 获取格式化的变化百分比文本
    func getChangePercentageText() -> String {
        guard let changePercentage = statistics?.changePercentage else {
            return ""
        }
        
        let sign = changePercentage >= 0 ? "+" : "-"
        return "\(sign) \(String(format: "%.1f", abs(changePercentage)))%"
    }
    
    /// 获取变化百分比颜色
    func getChangePercentageColor() -> Color {
        guard let changePercentage = statistics?.changePercentage else {
            return .textSecondary
        }
        
        return changePercentage >= 0 ? .success : .error
    }
    
    // MARK: - Private Methods
    
    /// 更新当前时间段的数据
    private func updateDataForSelectedPeriod() {
        let calendar = Calendar.current
        let now = Date()
        
        // 根据选中的时间段筛选数据
        let filteredData: [StepData]
        
        switch selectedPeriod {
        case .day:
            // 最近24小时的数据（按小时分组）
            filteredData = allStepData.filter { stepData in
                calendar.isDate(stepData.date, inSameDayAs: now)
            }.suffix(24)
        case .week:
            // 最近7天的数据
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
            filteredData = allStepData.filter { stepData in
                stepData.date >= weekAgo
            }.suffix(7)
        case .month:
            // 最近30天的数据
            let monthAgo = calendar.date(byAdding: .day, value: -30, to: now) ?? now
            filteredData = allStepData.filter { stepData in
                stepData.date >= monthAgo
            }.suffix(30)
        case .sixMonths:
            // 最近6个月的数据（每周一个数据点）
            let sixMonthsAgo = calendar.date(byAdding: .month, value: -6, to: now) ?? now
            filteredData = allStepData.filter { stepData in
                stepData.date >= sixMonthsAgo
            }.suffix(26) // 大约26周
        }
        
        currentStepData = Array(filteredData)
        
        // 计算统计信息
        calculateStatistics()
        
        // 更新Y轴刻度
        updateYAxisScale()
    }
    
    /// 计算统计信息
    private func calculateStatistics() {
        guard !currentStepData.isEmpty else {
            statistics = nil
            return
        }
        
        // 获取上一周期的平均值用于计算变化百分比
        let previousPeriodAverage = calculatePreviousPeriodAverage()
        
        statistics = SportsStatistics(
            stepData: currentStepData,
            period: selectedPeriod,
            previousPeriodAverage: previousPeriodAverage
        )
    }
    
    /// 计算上一周期的平均值
    private func calculatePreviousPeriodAverage() -> Double? {
        let calendar = Calendar.current
        let now = Date()
        
        let previousPeriodData: [StepData]
        
        switch selectedPeriod {
        case .day:
            let yesterday = calendar.date(byAdding: .day, value: -1, to: now) ?? now
            previousPeriodData = allStepData.filter { stepData in
                calendar.isDate(stepData.date, inSameDayAs: yesterday)
            }
        case .week:
            let twoWeeksAgo = calendar.date(byAdding: .day, value: -14, to: now) ?? now
            let oneWeekAgo = calendar.date(byAdding: .day, value: -7, to: now) ?? now
            previousPeriodData = allStepData.filter { stepData in
                stepData.date >= twoWeeksAgo && stepData.date < oneWeekAgo
            }
        case .month:
            let twoMonthsAgo = calendar.date(byAdding: .day, value: -60, to: now) ?? now
            let oneMonthAgo = calendar.date(byAdding: .day, value: -30, to: now) ?? now
            previousPeriodData = allStepData.filter { stepData in
                stepData.date >= twoMonthsAgo && stepData.date < oneMonthAgo
            }
        case .sixMonths:
            let oneYearAgo = calendar.date(byAdding: .month, value: -12, to: now) ?? now
            let sixMonthsAgo = calendar.date(byAdding: .month, value: -6, to: now) ?? now
            previousPeriodData = allStepData.filter { stepData in
                stepData.date >= oneYearAgo && stepData.date < sixMonthsAgo
            }
        }
        
        guard !previousPeriodData.isEmpty else { return nil }
        
        let totalSteps = previousPeriodData.reduce(0) { $0 + $1.steps }
        return Double(totalSteps) / Double(previousPeriodData.count)
    }
    
    /// 更新Y轴刻度
    private func updateYAxisScale() {
        guard !currentStepData.isEmpty else {
            yAxisMinValue = 0
            yAxisMaxValue = 15000
            return
        }
        
        let steps = currentStepData.map(\.steps)
        let minSteps = steps.min() ?? 0
        let maxSteps = steps.max() ?? 15000
        
        // 设置Y轴最小值（向下取整到千位）
        yAxisMinValue = max(0, (minSteps / 1000) * 1000 - 1000)
        
        // 设置Y轴最大值（向上取整到千位）
        yAxisMaxValue = ((maxSteps / 1000) + 1) * 1000 + 1000
        
        // 确保最小范围
        if yAxisMaxValue - yAxisMinValue < 5000 {
            yAxisMaxValue = yAxisMinValue + 5000
        }
    }
    
    /// 生成Mock数据
    private func generateMockData() {
        allStepData.removeAll()
        
        let calendar = Calendar.current
        let now = Date()
        
        // 生成最近6个月的数据
        for i in 0..<180 {
            guard let date = calendar.date(byAdding: .day, value: -i, to: now) else { continue }
            
            // 模拟真实的步数数据（工作日和周末不同，有一定随机性）
            let isWeekend = calendar.isDateInWeekend(date)
            let baseSteps = isWeekend ? 6000 : 9000
            let randomVariation = Int.random(in: -2000...4000)
            let steps = max(1000, baseSteps + randomVariation)
            
            // 模拟卡路里数据
            let calories = Double(steps) * 0.04 // 大约每步0.04卡路里
            
            let stepData = StepData(date: date, steps: steps, calories: calories)
            allStepData.append(stepData)
        }
        
        // 按日期排序
        allStepData.sort { $0.date < $1.date }
    }
}
