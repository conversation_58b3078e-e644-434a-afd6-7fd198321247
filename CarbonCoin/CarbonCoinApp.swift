//
//  CarbonCoinApp.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//
import SwiftUI

@main
struct CarbonCoinApp: App {
    init() {
        // 清除默认 Window 背景
        UIWindow.appearance().backgroundColor = .clear

        // 设置 NavigationBar 背景透明
        let appearance = UINavigationBarAppearance()
        appearance.configureWithTransparentBackground()
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance

        // 设置 ScrollView 背景透明
        UITableView.appearance().backgroundColor = .clear
        UICollectionView.appearance().backgroundColor = .clear
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.dark)  // 强制使用 Dark Mode
        }
    }
}
